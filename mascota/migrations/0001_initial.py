# Generated by Django 5.2.4 on 2025-08-25 22:55

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('cuidador', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Ma<PERSON><PERSON>',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nombre', models.Char<PERSON>ield(max_length=100)),
                ('especie', models.Char<PERSON>ield(max_length=100)),
                ('edad', models.IntegerField()),
                ('cuidador', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='cuidador.cuidador')),
            ],
        ),
    ]
