from django.db import models
from cuidador.models import Cuidador

# Create your models here.
class Mascota(models.Model):
    nombre = models.CharField(max_length=100)
    especie = models.CharField(max_length=100)
    edad = models.IntegerField()
    cuidador = models.ForeignKey(Cuidador, on_delete=models.CASCADE)

    def __str__(self):
        return f"{self.nombre} - {self.especie} - {self.edad} años - {self.cuidador}"
