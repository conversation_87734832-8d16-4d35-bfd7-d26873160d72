from django.db import models

# Create your models here.
class procedimiento(models.Model):
    nombre = models.CharField(max_length=100)
    descripcion = models.TextField()
    costo = models.DecimalField(max_digits=10, decimal_places=2)
    mascota = models.ForeignKey('mascota.Mascota', on_delete=models.CASCADE)
    fecha = models.DateField()

    def __str__(self):
        return f"{self.nombre} - {self.costo} Pesos - {self.fecha}"