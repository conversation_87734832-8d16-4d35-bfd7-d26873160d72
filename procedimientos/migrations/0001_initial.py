# Generated by Django 5.2.4 on 2025-08-25 22:55

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('mascota', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='procedimiento',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('nombre', models.CharField(max_length=100)),
                ('descripcion', models.TextField()),
                ('costo', models.DecimalField(decimal_places=2, max_digits=10)),
                ('fecha', models.DateField()),
                ('mascota', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mascota.mascota')),
            ],
        ),
    ]
